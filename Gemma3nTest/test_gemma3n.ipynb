{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fb4ed618", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'AutoProcessor' from 'transformers' (/usr/local/lib/python3.10/dist-packages/transformers/__init__.py)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtransformers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AutoProcessor, AutoModelForImageTextToText\n\u001b[1;32m      4\u001b[0m GEMMA_MODEL_ID \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgoogle/gemma-3n-E4B-it\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      6\u001b[0m processor \u001b[38;5;241m=\u001b[39m AutoProcessor\u001b[38;5;241m.\u001b[39mfrom_pretrained(GEMMA_MODEL_ID, device_map\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'AutoProcessor' from 'transformers' (/usr/local/lib/python3.10/dist-packages/transformers/__init__.py)"]}], "source": ["import torch\n", "from transformers import AutoProcessor, AutoModelForImageTextToText\n", "\n", "GEMMA_MODEL_ID = \"google/gemma-3n-E4B-it\"\n", "\n", "processor = AutoProcessor.from_pretrained(GEMMA_MODEL_ID, device_map=\"auto\")\n", "model = AutoModelForImageTextToText.from_pretrained(\n", "            GEMMA_MODEL_ID, torch_dtype=\"auto\", device_map=\"auto\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "5f20ba84", "metadata": {}, "outputs": [], "source": ["\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"type\": \"audio\", \"audio\": \"http://localhost/recording_01.wav\"},\n", "            {\"type\": \"audio\", \"audio\": \"http://localhost/recording_02.wav\"},\n", "            {\"type\": \"audio\", \"audio\": \"http://localhost/recording_03.wav\"},\n", "            {\"type\": \"text\", \"text\": \"Transcribe these audio files in order\"},\n", "        ]\n", "    }\n", "]\n", "\n", "input_ids = processor.apply_chat_template(\n", "        messages,\n", "        add_generation_prompt=True,\n", "        tokenize=True, return_dict=True,\n", "        return_tensors=\"pt\",\n", ")\n", "input_ids = input_ids.to(model.device, dtype=model.dtype)\n", "\n", "outputs = model.generate(**input_ids, max_new_tokens=64)\n", "\n", "text = processor.batch_decode(\n", "    outputs,\n", "    skip_special_tokens=False,\n", "    clean_up_tokenization_spaces=False\n", ")\n", "print(text[0])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}