[project]
name = "DeepFilterLib"
version = "0.5.7-pre"
classifiers = ["Programming Language :: Rust"]
requires-python = ">=3.8"
dependencies = ["numpy >= 1.22"]

[build-system]
requires = ["maturin>=1.3,<1.5"]
build-backend = "maturin"

[tool.maturin]
sdist-include = ["Cargo.lock"]
strip = true

[tool.black]
line-length = 100
target-version = ["py38", "py39", "py310", "py311"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 100
skip_gitignore = true
known_first_party = ["df", "libdf", "libdfdata"]
