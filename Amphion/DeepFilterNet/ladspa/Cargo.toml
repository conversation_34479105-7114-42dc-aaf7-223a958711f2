[package]
name = "deep-filter-ladspa"
version = "0.5.7-pre"
authors = ["<PERSON><PERSON><PERSON>"]
edition = "2021"
license = "MIT/Apache-2.0"

[lib]
crate-type = ["cdylib"]

[features]
dbus = ["dep:zbus", "dep:event-listener"]
default-model-ll = ["deep_filter/default-model-ll"]
default = ["default-model-ll"]

[dependencies]
deep_filter = { path = "../libDF", default-features = false, features = [
  "tract",
  "use-jemalloc",
  "default-model",
] }
ladspa = "0.3.4"
ndarray = "^0.15"
env_logger = "0.10"
uuid = { version = "1.2.1", features = ["v4", "fast-rng"] }
log = { version = "0.4", features = ["std"] }
zbus = { version = "3.6", optional = true }
event-listener = { version = "2.5", optional = true }
