# DeepFilter Noise Canceling Source for you microphone
#
# 1. Copy this file into a conf.d/ directory such as:
#     /etc/pipewire/filter-chain.conf.d/
#     or locally:
#     ~/.config/pipewire/filter-chain.conf.d/
#
# 2. Install the deep_filter plugin globally to:
#     /usr/lib/ladspa/libdeep_filter_ladspa.so
#     or locally:
#     ~/.ladspa/libdeep_filter_ladspa.so
#
# 3. Adjust the plugin path (line 35) accordingly. If you installed the plugin
#     locally in your home directory, make sure to provide an absolute path here
#     (i.e. /home/<USER>/.ladspa/) and do not use '~' or '$HOME'!
#
# 4. You may limit the noise attenuation (i.e. don't reduce all noise) by setting
#     'Attenuation Limit (dB)' to a smaller value. For little noise reduction, try
#     6-12, for medium noise reduction 18-24 dB. 100 dB means no attenuation limit.
#
# 5. Run the DeepFilter pipewire filter via `pipewire -c filter-chain.conf`
#
# 6. Select the DeepFilter microphone in your VoIP application like Zoom or Discord

context.modules = [
    { name = libpipewire-module-filter-chain
        args = {
            node.description = "DeepFilter Noise Canceling Source"
            media.name       = "DeepFilter Noise Canceling Source"
            filter.graph = {
                nodes = [
                    {
                        type   = ladspa
                        name   = "DeepFilter Mono"
                        plugin = /usr/lib/ladspa/libdeep_filter_ladspa.so
                        label  = deep_filter_mono
                        control = {
                            "Attenuation Limit (dB)" 100
                        }
                    }
                ]
            }
            audio.rate = 48000
            audio.position = [MONO]
            capture.props = {
                node.passive = true
            }
            playback.props = {
                media.class = Audio/Source
            }
        }
    }
]
