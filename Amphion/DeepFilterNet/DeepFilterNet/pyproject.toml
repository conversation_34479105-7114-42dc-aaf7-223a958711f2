[tool.poetry]
name = "DeepFilterNet"
version = "0.5.7-pre"
description = "Noise supression using deep filtering"
authors = ["<PERSON><PERSON><PERSON>"]
repository = "https://github.com/Rikorose/DeepFilterNet"
keywords = ["noise reduction", "neural network"]
classifiers = [
  "Topic :: Multimedia :: Sound/Audio :: Speech",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "Topic :: Software Development :: Libraries :: Application Frameworks",
  "Topic :: Utilities",
  "Programming Language :: Rust",
  "Programming Language :: Python",
]
license = "MIT"
packages = [{ include = "df" }]
include = [
  { path = "pretrained_models/DeepFilterNet/config.ini" },
  { path = "pretrained_models/DeepFilterNet/checkpoints/*" },
  { path = "pretrained_models/DeepFilterNet2/config.ini" },
  { path = "pretrained_models/DeepFilterNet2/checkpoints/*" },
]

[tool.poetry.dependencies]
deepfilterlib = { path = "../pyDF/" }
deepfilterdataloader = { path = "../pyDF-data/", optional = true }
python = ">=3.8,<4.0"
numpy = ">=1.22,<2.0"
loguru = ">=0.5"
appdirs = "^1.4"
requests = "^2.27"
packaging = ">=23,<25"
sympy = ">=1.6"
soundfile = { version = ">=0.10,<0.13", optional = true }
icecream = { version = ">=2,<3", optional = true }
pystoi = { version = ">=0.3,<0.5", optional = true }
pesq = { version = ">=0.0.3,<0.0.5", optional = true }
scipy = { version = "^1", optional = true }
onnxruntime = { version = "^1.15", optional = true }

[tool.poetry.extras]
train = ["deepfilterdataloader", "icecream"]
eval = ["pystoi", "pesq", "scipy"]
soundfile = ["soundfile"]
dnsmos-local = ["onnxruntime"]

[tool.poetry.scripts]
deepFilter = "df.enhance:run"
deep-filter-py = "df.enhance:run"

[tool.poetry.dev-dependencies]
poethepoet = "^0.26"

[tool.poe.tasks]
install-torch-cuda11 = "pip install torch==2.1+cu118 torchaudio==2.1 --extra-index-url https://download.pytorch.org/whl/cu118"
install-torch-cuda12 = "pip install torch==2.1+cu121 torchaudio==2.1 --extra-index-url https://download.pytorch.org/whl/cu121"
install-torch-cpu = "pip install torch==2.1+cpu torchaudio==2.1 --extra-index-url https://download.pytorch.org/whl/cpu"
install-eval-utils = "python -m pip install -r requirements_eval.txt"
install-dnsmos-utils = "python -m pip install -r requirements_dnsmos.txt"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py38", "py39", "py310"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 100
skip_gitignore = true
known_first_party = ["df", "libdf", "libdfdata"]
