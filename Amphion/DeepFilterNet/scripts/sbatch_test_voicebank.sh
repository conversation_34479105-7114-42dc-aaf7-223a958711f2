#!/bin/bash
#SBATCH --job-name=df
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=1
#SBATCH --mem=8000
#SBATCH --gres=gpu:1
#SBATCH -o /cluster/%u/logs/%j.out
#SBATCH -e /cluster/%u/logs/%j.out
#SBATCH --mail-type=ALL
# Time limit format: "hours:minutes:seconds"
#SBATCH --time=04:00:00

set -e

# Cluster directory containing data, project code, logging, summaries,
# checkpoitns, etc.
export CLUSTER=/net/cluster/$USER
cd "$CLUSTER"

# Workaround for our cluster
export WORKON_HOME="/cluster/$USER/.cache"
export XDG_CACHE_DIR="/cluster/$USER/.cache"
export PYTHONUSERBASE="/cluster/$USER/.python_packages"

PROJECT_NAME=DeepFilterNet
BRANCH=${BRANCH:-main}
DATA_DIR=${DATA_DIR:-$CLUSTER/Data/voicebank-demand-test}     # Set to the directory containing the HDF5s
PYTORCH_JIT=${PYTORCH_JIT:-1}                # Set to 0 to disable pytorch JIT compilation

echo "Started sbatch script at $(date) in $(pwd)"

echo "Found cuda devices: $CUDA_VISIBLE_DEVICES"
nvidia-smi -L || echo "nvidia-smi not found"
echo "Running on host: $(hostname)"

# Check base dir file
if [[ -z $1 ]]; then
  echo >&2 "No model base directory provided!"
  exit
fi
if [[ ! -d $1 ]]; then
  echo >&2 "Model base directory not found at $1!"
  exit
fi

BASE_DIR=$(readlink -f "$1")

echo "Got base_dir: $BASE_DIR"
MODEL_NAME=$(basename "$BASE_DIR")

# Git project setup.
# Creates a separate code directory so that changes of the source code don't
# have any impact on the automatic resubmission process. Furthermore, a specific
# branch or commit can be specified that will be tried to checkout. By default,
# the currently active branch is used.
PROJECT_BRANCH=${BRANCH:-$PROJECT_BRANCH_CUR}
if [[ -n $2 ]]; then
  if [[ ! -d $2 ]]; then
    echo >&2 "Project home not found at $2!"
    exit
  fi
  PROJECT_HOME=$2
else
  PROJECT_ClUSTER_HOME=$CLUSTER/$PROJECT_NAME/
  PROJECT_HOME=$CLUSTER/sbatch-$PROJECT_NAME/$MODEL_NAME/
  mkdir -p "$PROJECT_HOME"
  echo "Copying repo to $PROJECT_HOME"
  cd "$PROJECT_ClUSTER_HOME"
  rsync -avq --include .git \
    --exclude-from="$(git -C "$PROJECT_ClUSTER_HOME" ls-files --exclude-standard -oi --directory >.git/ignores.tmp && echo .git/ignores.tmp)" \
    "$PROJECT_ClUSTER_HOME" "$PROJECT_HOME" --delete
fi
if [ -n "$3" ]; then
  # Checkout specified branch from previous job
  PROJECT_BRANCH_CUR=$3
else
  # Use current branch of project on /cluster
  PROJECT_BRANCH_CUR=$(git -C "$PROJECT_ClUSTER_HOME" rev-parse --abbrev-ref HEAD)
fi
echo "Running on branch $PROJECT_BRANCH in dir $PROJECT_HOME"
if [ "$PROJECT_BRANCH_CUR" != "$PROJECT_BRANCH" ]; then
  stash="stash_$SLURM_JOB_ID"
  git -C "$PROJECT_HOME" stash save "$stash"
  git -C "$PROJECT_HOME" checkout "$PROJECT_BRANCH"
  stash_idx=$(git -C "$PROJECT_HOME" stash list | grep "$stash" | cut -d: -f1)
  if [ -n "$stash_idx" ] && [ "$stash_idx" != " " ]; then
    # Try to apply current stash; If not possible just proceed.
    if ! git -C "$PROJECT_HOME" stash pop "$stash_idx"; then
      echo "Could not apply stash to branch $PROJECT_BRANCH"
      git -C "$PROJECT_HOME" checkout -f
    fi
  fi
fi

# Setup conda environment.
# This installs miniconda environment if not existing, pytorch with cuda
# integration and pip packages in requirements.txt
. "$PROJECT_HOME"/scripts/setup_env.sh --source-only
setup_env "$CLUSTER" "$PROJECT_HOME" "$MODEL_NAME"

cd "$PROJECT_HOME"/DeepFilterNet/df/

# Start training
printf "\n***Starting training***\n\n"

PYTHONPATH="$PROJECT_HOME/DeepFilterNet/" python scripts/test_voicebank_demand.py \
  -m "$BASE_DIR" \
  "$DATA_DIR"

trainprocess=$!
echo "Started trainprocess: $trainprocess"

wait $trainprocess
echo "Training stopped"
